#!/usr/bin/env python3
"""
Setup script to install and configure ngrok for TradingView webhook testing
"""

import os
import sys
import subprocess
import requests
import zipfile
import json
from pathlib import Path

def check_ngrok_installed():
    """Check if ngrok is already installed"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ngrok is already installed: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    return False

def download_ngrok():
    """Download ngrok for Windows"""
    print("📥 Downloading ngrok for Windows...")
    
    # Create ngrok directory
    ngrok_dir = Path("ngrok")
    ngrok_dir.mkdir(exist_ok=True)
    
    # Download URL for Windows 64-bit
    download_url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip"
    
    try:
        response = requests.get(download_url, stream=True)
        response.raise_for_status()
        
        zip_path = ngrok_dir / "ngrok.zip"
        with open(zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print("✅ Downloaded ngrok.zip")
        
        # Extract the zip file
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(ngrok_dir)
        
        # Remove zip file
        zip_path.unlink()
        
        ngrok_exe = ngrok_dir / "ngrok.exe"
        if ngrok_exe.exists():
            print(f"✅ ngrok extracted to: {ngrok_exe.absolute()}")
            return str(ngrok_exe.absolute())
        else:
            print("❌ Failed to extract ngrok.exe")
            return None
            
    except Exception as e:
        print(f"❌ Failed to download ngrok: {e}")
        return None

def setup_ngrok_auth():
    """Setup ngrok authentication (optional but recommended)"""
    print("\n🔑 Setting up ngrok authentication...")
    print("1. Go to https://dashboard.ngrok.com/get-started/your-authtoken")
    print("2. Sign up for a free account (if you don't have one)")
    print("3. Copy your authtoken")
    
    authtoken = input("\nEnter your ngrok authtoken (or press Enter to skip): ").strip()
    
    if authtoken:
        try:
            if check_ngrok_installed():
                cmd = ['ngrok', 'config', 'add-authtoken', authtoken]
            else:
                ngrok_path = download_ngrok()
                if not ngrok_path:
                    return False
                cmd = [ngrok_path, 'config', 'add-authtoken', authtoken]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ ngrok authtoken configured successfully")
                return True
            else:
                print(f"❌ Failed to configure authtoken: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error configuring authtoken: {e}")
            return False
    else:
        print("⚠️  Skipping authtoken setup (you can add it later)")
        return True

def start_ngrok_tunnel(port=8000):
    """Start ngrok tunnel for the specified port"""
    print(f"\n🚀 Starting ngrok tunnel for port {port}...")
    
    try:
        if check_ngrok_installed():
            cmd = ['ngrok', 'http', str(port)]
        else:
            ngrok_path = download_ngrok()
            if not ngrok_path:
                return None
            cmd = [ngrok_path, 'http', str(port)]
        
        print("Starting ngrok tunnel...")
        print("This will create a public URL that forwards to your local Flask app")
        print("Keep this terminal window open while testing!")
        print("\nPress Ctrl+C to stop the tunnel when done")
        
        # Start ngrok in a new process
        process = subprocess.Popen(cmd)
        
        # Wait a moment for ngrok to start
        import time
        time.sleep(3)
        
        # Try to get the public URL from ngrok API
        try:
            response = requests.get('http://localhost:4040/api/tunnels')
            if response.status_code == 200:
                tunnels = response.json()['tunnels']
                if tunnels:
                    public_url = tunnels[0]['public_url']
                    print(f"\n🎉 ngrok tunnel is running!")
                    print(f"📡 Public URL: {public_url}")
                    print(f"🔗 Use this URL in TradingView: {public_url}/webhook")
                    return public_url
        except:
            pass
        
        print("\n🎉 ngrok tunnel started!")
        print("📡 Check the ngrok terminal for your public URL")
        print("🔗 Use the HTTPS URL in TradingView alerts")
        
        return process
        
    except Exception as e:
        print(f"❌ Failed to start ngrok tunnel: {e}")
        return None

def main():
    """Main setup function"""
    print("🔧 ngrok Setup for TradingView Webhook Testing")
    print("=" * 50)
    
    # Check if Flask app is running
    try:
        response = requests.get('http://localhost:8000/')
        if response.status_code == 200:
            print("✅ Flask app is running on localhost:8000")
        else:
            print("⚠️  Flask app responded but with unexpected status")
    except:
        print("❌ Flask app is not running on localhost:8000")
        print("   Please start it first: python app.py")
        return
    
    # Setup ngrok
    if not check_ngrok_installed():
        print("📦 ngrok not found, downloading...")
        if not download_ngrok():
            print("❌ Failed to download ngrok")
            return
    
    # Setup authentication (optional)
    setup_ngrok_auth()
    
    # Start tunnel
    print("\n" + "=" * 50)
    print("🚀 Ready to start ngrok tunnel!")
    print("This will create a public URL for your Flask app")
    
    start_tunnel = input("\nStart ngrok tunnel now? (y/n): ").lower().strip()
    if start_tunnel == 'y':
        start_ngrok_tunnel()
    else:
        print("\n📝 To start the tunnel manually later:")
        print("   ngrok http 8000")

if __name__ == "__main__":
    main()

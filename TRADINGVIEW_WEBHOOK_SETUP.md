# 🎯 Correct TradingView Webhook Setup Guide

Based on research, here's the **correct way** to set up TradingView webhooks with your Pine Script strategy.

## ✅ Fixed Pine Script (Copy This)

```pinescript
//@version=5
strategy(title="[RS]Murrey's Math Lines Channel Strategy", overlay=true)

// Parameters
length00 = input.int(115, title="Length", minval=1)
fmultiplier = input.float(0.121, title="Multiplier", step=0.001)

// Webhook Configuration
webhook_url = input.string("https://your-app-name.up.railway.app/webhook", title="Webhook URL", group="Webhook Settings")
webhook_secret = input.string("testsecret", title="Webhook Secret", group="Webhook Settings")
coin_symbol = input.string("BTC", title="Coin Symbol (BTC, ETH, etc.)", group="Webhook Settings")
enable_webhooks = input.bool(true, title="Enable Webhook Alerts", group="Webhook Settings")

// Calculate Murrey Math lines
hhi = ta.highest(high, length00)
llo = ta.lowest(low, length00)
midline = (hhi + llo) / 2
distance = (hhi - llo) * fmultiplier
upper_line_4_8 = midline + distance * 4
upper_line_3_8 = midline + distance * 3
upper_line_2_8 = midline + distance * 2
upper_line_1_8 = midline + distance * 1
lower_line_1_8 = midline - distance * 1
lower_line_2_8 = midline - distance * 2
lower_line_3_8 = midline - distance * 3
lower_line_4_8 = midline - distance * 4

// Plot Murrey Math lines
plot(upper_line_4_8, color=color.green, linewidth=1)
plot(upper_line_3_8, color=color.gray, linewidth=1)
plot(upper_line_2_8, color=color.gray, linewidth=1)
plot(upper_line_1_8, color=color.gray, linewidth=1)
plot(midline, color=color.yellow, linewidth=3)
plot(lower_line_1_8, color=color.gray, linewidth=1)
plot(lower_line_2_8, color=color.gray, linewidth=1)
plot(lower_line_3_8, color=color.gray, linewidth=1)
plot(lower_line_4_8, color=color.red, linewidth=1)

// EMA
len = input.int(16, minval=1, title="EMA Length")
src = input(close, title="EMA Source")
out = ta.ema(src, len)
plot(out, title="EMA", color=color.blue)

// Entry and exit signals
buy_signal = ta.crossover(close, lower_line_4_8) and out < midline
sell_signal = ta.crossunder(close, upper_line_4_8) and out > midline
ema_buy = ta.crossover(out,lower_line_4_8)
ema_sell = ta.crossunder(out,upper_line_4_8)

// Webhook Alert Messages (Fixed - No timestamp needed)
buy_alert_message = '{"coin": "' + coin_symbol + '", "action": "BUY", "market_order": "1"}'
sell_alert_message = '{"coin": "' + coin_symbol + '", "action": "SELL", "market_order": "1"}'

// Strategy logic with webhook alerts
if (buy_signal) or (ema_buy)
    if enable_webhooks
        strategy.entry("Buy", strategy.long, qty=1, comment="Buy", alert_message=buy_alert_message)
    else
        strategy.entry("Buy", strategy.long, qty=1, comment="Buy")
        
else if (sell_signal) or (ema_sell)
    if enable_webhooks
        strategy.entry("Sell", strategy.short, qty=1, comment="Sell", alert_message=sell_alert_message)
    else
        strategy.entry("Sell", strategy.short, qty=1, comment="Sell")
```

## 🔧 How to Set Up TradingView Alert (Step by Step)

### Step 1: Add Strategy to Chart
1. Copy the Pine Script above
2. Paste into TradingView Pine Editor
3. Click "Add to Chart"
4. Configure webhook settings in strategy settings

### Step 2: Create Alert
1. **Right-click on chart** → "Add Alert"
2. **Condition**: Select your strategy name from dropdown
3. **Options**: 
   - ✅ "Once Per Bar Close" (recommended)
   - ✅ "Webhook URL"
4. **Webhook URL**: `http://localhost:8000/webhook` (for testing)
5. **Message**: `{{strategy.order.alert_message}}` ⚠️ **This is crucial!**

### Step 3: Add Security Header
In the alert dialog:
1. Click "Webhook URL" section
2. Add custom header:
   - **Name**: `X-Webhook-Secret`
   - **Value**: `testsecret`

## 📨 What Gets Sent

When your strategy triggers, TradingView will send:

**For BUY signal:**
```json
{"coin": "BTC", "action": "BUY", "market_order": "1"}
```

**For SELL signal:**
```json
{"coin": "BTC", "action": "SELL", "market_order": "1"}
```

## ✅ Key Fixes Made

1. **Fixed `str()` error**: Removed timestamp (not needed)
2. **Simplified JSON**: Clean format your Flask app expects
3. **Proper alert setup**: Uses `{{strategy.order.alert_message}}`
4. **Security header**: Webhook secret validation

## 🧪 Testing Process

1. **Start Flask app**: `python app.py`
2. **Test webhook format**: `python test_webhook_format.py`
3. **Add strategy to TradingView**
4. **Create alert with settings above**
5. **Monitor Flask logs** for incoming webhooks

## ⚠️ Important Notes

- **TradingView Pro required** for webhook alerts
- **Use `{{strategy.order.alert_message}}`** exactly as shown
- **Test locally first** before deploying
- **Monitor logs** to verify webhooks are received

Your Flask app is already perfect - it handles this JSON format correctly and converts `"coin": "BTC"` to `"symbol": "BTCUSD"` automatically!

<<<<<<< HEAD
# 🚀 TradingView → Binance US Automated Trading Bot

A production-ready Flask webhook server that automatically executes trades on Binance US when TradingView alerts are triggered by your Pine Script strategy.

## ✅ **System Status: READY FOR DEPLOYMENT**

- ✅ Flask webhook server with Binance US integration
- ✅ Murrey's Math Lines strategy with webhook alerts
- ✅ Security features (webhook secrets, emergency stops)
- ✅ Percentage-based quantity management
- ✅ Comprehensive logging and error handling
- ✅ Railway deployment configuration

## ✅ Features

- 🚀 **Webhook Integration**: Real-time TradingView alert processing
- 📈 **Binance US API**: Direct integration with Binance US exchange
- 🔒 **Security**: Webhook secret validation and secure API key management
- 📊 **Order Execution**: Market orders with small quantities for safety
- 📝 **Comprehensive Logging**: Detailed logging for troubleshooting
- 🌐 **Free Deployment**: Railway hosting with automatic scaling
- ✅ **Built-in Testing**: Test endpoints for validation
- 📋 **Health Monitoring**: Status checks and balance tracking

## Quick Setup

### 1. Install Dependencies

```bash
# Create virtual environment
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Mac/Linux

# Install requirements
pip install -r requirements.txt
```

### 2. Configure Environment Variables

Copy `.env.template` to `.env` and add your credentials:

```bash
copy .env.template .env
```

Edit `.env` with your actual values:
```env
# Binance API Configuration
BINANCE_API_KEY=your_actual_api_key_here
BINANCE_SECRET_KEY=your_actual_secret_key_here

# Flask Configuration
FLASK_ENV=production
SECRET_KEY=your_random_secret_key_here (32+ characters)

# Webhook Security
WEBHOOK_SECRET=your_webhook_secret_here

# Railway Deployment (Optional)
PORT=8000
```

### 3. Test API Connection

```bash
python test_api.py
```

### 4. Run the Application

```bash
python app.py
```

The server starts on `http://localhost:8000`. Visit `http://localhost:8000` to check status.

### 5. Test Trading

Test with the test endpoint:
```bash
curl -X POST http://localhost:8000/test \
  -H "Content-Type: application/json" \
  -d '{"action": "BUY", "symbol": "BTCUSDT"}'
```

## 📡 API Endpoints

### Health Check
```bash
GET /
```
Response shows system status, Binance connection, and timestamp.

### Webhook Endpoint (TradingView Alerts)
```bash
POST /webhook
Headers:
  Content-Type: application/json
  X-Webhook-Secret: your_webhook_secret

Body:
{
  "action": "BUY",
  "symbol": "BTCUSDT"
}
```

### Test Endpoint
```bash
POST /test
Headers:
  Content-Type: application/json

Body:
{
  "action": "BUY",
  "symbol": "BTCUSDT"
}
```

### Balance Check
```bash
GET /balance
```
Returns current account balances and timestamp.

## 🚀 Deployment

### Railway (Recommended)

1. **Initialize Git Repository**:
```bash
git init
git add .
git commit -m "Initial TradingView Binance webhook bot"
git remote add origin https://github.com/yourusername/tradingview-binance-webhook.git
git push -u origin main
```

2. **Deploy on Railway**:
   - Go to railway.app
   - Create new project
   - Select "Deploy from GitHub repo"
   - Add environment variables from your `.env` file:
     ```
     BINANCE_API_KEY=your_key
     BINANCE_SECRET_KEY=your_secret
     WEBHOOK_SECRET=your_secret
     SECRET_KEY=your_flask_secret
     PORT=8000
     ```
   - Deploy and get your webhook URL

### Production Server

For local production deployment:
```bash
# Install production dependencies
pip install gunicorn

# Start server
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

## 🔗 TradingView Alert Configuration

### Alert Setup
1. In TradingView, create a new alert on your desired chart
2. Set the alert conditions (e.g., price crosses above moving average)
3. In the "Webhook URL" field, enter:
   ```
   https://your-domain.com/webhook
   ```
4. Enable "Once per bar" to avoid duplicate alerts
5. Add "JSON" as the POST payload format

### Alert Payload Format
```json
{
  "action": "BUY",
  "symbol": "BTCUSDT"
}
```

### Security Configuration
Add the webhook secret to your TradingView alert headers:
- **Header name**: `X-Webhook-Secret`
- **Header value**: `your_webhook_secret`

### Pine Script Integration
```pinescript
//@version=5
strategy("Your Strategy", overlay=true)

// Webhook configuration
webhook_url = input.string("https://your-app-name.up.railway.app/webhook", title="Webhook URL")
webhook_secret = input.string("your_secret", title="Webhook Secret")
symbol_name = input.string("BTCUSDT", title="Trading Symbol")

// Your strategy logic
buy_signal = crossover(close, sma(20, 20))
sell_signal = crossunder(close, sma(20, 20))

// Webhook alert messages with headers
buy_message = '{"action": "BUY", "symbol": "' + symbol_name + '"}'
sell_message = '{"action": "SELL", "symbol": "' + symbol_name + '"}'

// Buy alert with headers
if (buy_signal)
    strategy.entry("Buy", strategy.long, alert_message=buy_message, 
                 alertcomment="{" + "webhook_url: '" + webhook_url + "', headers: {\"X-Webhook-Secret\": \"" + webhook_secret + "\"}}")

// Sell alert with headers  
if (sell_signal)
    strategy.entry("Sell", strategy.short, alert_message=sell_message,
                 alertcomment="{" + "webhook_url: '" + webhook_url + "', headers: {\"X-Webhook-Secret\": \"" + webhook_secret + "\"}}")
```

## 🛡️ Security Features

### Webhook Secret Validation
- Validates `X-Webhook-Secret` header for all webhook requests
- URL-decodes header values to handle special characters
- Prevents unauthorized access to trading endpoints

### API Key Security
- Never enable withdrawal permissions on API keys
- Store credentials in environment variables (never in code)
- Support for API key IP restrictions
- Secure logging without exposing sensitive data

### Environment Variables
All sensitive data stored in `.env` file:
- Binance API credentials
- Flask secret key
- Webhook validation secret
- Never commit `.env` to version control (see `.gitignore`)

## ⚙️ Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `BINANCE_API_KEY` | Binance US API key | Required |
| `BINANCE_SECRET_KEY` | Binance US API secret | Required |
| `WEBHOOK_SECRET` | Webhook validation secret | `testsecret` |
| `SECRET_KEY` | Flask secret key | Random |
| `FLASK_ENV` | Flask environment | `production` |
| `PORT` | Server port | `8000` |

### Order Quantities
Currently using fixed small quantities for live wallet safety:
- **BTC pairs**: 0.00001 BTC (~$1.11 at current price)
- **ETH pairs**: 0.001 ETH
- **Other pairs**: Minimum allowed by exchange

## 💰 Cost Analysis

| Service | Cost | Notes |
|---------|------|-------|
| Railway Hosting | $0 (Free tier) | Up to 7k requests/hour |
| Binance US Trading | 0.1% per trade | Standard trading fee |
| Python Dependencies | $0 | Open source |

## 📊 Order Execution Details

### Success Response
```json
{
  "status": "success",
  "message": "SELL order executed successfully",
  "order_details": {
    "order_id": **********,
    "quantity": 1e-05,
    "side": "SELL",
    "status": "FILLED",
    "success": true,
    "symbol": "BTCUSDT"
  },
  "timestamp": "2025-09-09T21:54:19.148618+00:00"
}
```

### Error Response
```json
{
  "status": "error",
  "message": "Order execution failed",
  "error": "Error details here",
  "timestamp": "2025-09-09T21:54:19.148618+00:00"
}
```

## 🔧 Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check API keys in `.env`
   - Ensure account has trading permissions
   - Verify API key IP restrictions
   - Confirm using Binance US (not international)

2. **Order Execution Failed**
   - Check account balance (minimum order value)
   - Verify symbol format (BTCUSDT, ETHUSD, etc.)
   - Ensure quantity meets minimum requirements

3. **Webhook Not Working**
   - Verify webhook URL in TradingView
   - Check webhook secret match
   - Verify JSON payload format
   - Check for encoding issues

4. **"Illegal characters found in parameter 'quantity'"**
   - System automatically handles quantity formatting
   - Check logs for detailed formatting process

### Debug Mode
Enable debug logging by setting `FLASK_ENV=development` in your environment variables.

### Logs
- **Local**: `trading_bot.log` - Detailed execution logs
- **Production**: Railway dashboard logs
- **Monitoring**: Check for successful order executions

## 🧪 Testing

### Local Testing
```bash
# Test webhook
curl -X POST http://localhost:8000/webhook \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Secret: testsecret" \
  -d '{"action": "BUY", "symbol": "BTCUSDT"}'

# Test endpoint
curl -X POST http://localhost:8000/test \
  -H "Content-Type: application/json" \
  -d '{"action": "SELL", "symbol": "BTCUSDT"}'

# Check balance
curl http://localhost:8000/balance

# Health check
curl http://localhost:8000/
```

### Testing Checklist
- [ ] API connection test (`test_api.py`)
- [ ] Test order execution (`/test` endpoint)
- [ ] Webhook validation with secret
- [ ] Balance checking
- [ ] Error handling

## ⚠️ Important Notes

### Safety First
- Uses minimal order quantities (0.00001 BTC) to protect your live trading account
- Always test with small amounts before full deployment
- Monitor logs regularly for any unusual activity
- Never enable withdrawal permissions on API keys

### Binance US Specific
- Only supports Binance US trading pairs
- Different API endpoint than international Binance
- Account must be verified for trading
- Different trading pairs and fees

### Rate Limits
- Be aware of Binance API rate limits (1200/min, 10k/day)
- Consider adding delays between rapid-fire alerts
- Monitor for rate limit errors in logs

## 📞 Support

For issues and questions:
1. **Check logs first** - `trading_bot.log` contains detailed execution info
2. **Verify API credentials** - Ensure correct Binance US API keys
3. **Test endpoints** - Use `/test` endpoint for validation
4. **Review TradingView config** - Check alert format and headers
5. **Check Railway dashboard** - For deployed applications

## 📄 License

This project is for educational and commercial use. Use responsibly and at your own risk.

**Disclaimer**: This is a trading bot that executes real orders. Understand the risks involved in automated trading and never invest more than you can afford to lose.
=======
# webhook
tradingview to binance.us webhook
>>>>>>> 281afaf35bb9d145dbca034bac59d62d2313f3dd

//@version=5
strategy(title="[RS]Murrey's Math Lines Channel Strategy", overlay=true)

// Parameters
length00 = input.int(115, title="Length", minval=1)
fmultiplier = input.float(0.121, title="Multiplier", step=0.001)

// Webhook Configuration
webhook_url = input.string("https://your-app-name.up.railway.app/webhook", title="Webhook URL", group="Webhook Settings")
webhook_secret = input.string("testsecret", title="Webhook Secret", group="Webhook Settings")
coin_symbol = input.string("BTC", title="Coin Symbol (BTC, ETH, etc.)", group="Webhook Settings")
enable_webhooks = input.bool(true, title="Enable Webhook Alerts", group="Webhook Settings")

// Calculate Murrey Math lines
hhi = ta.highest(high, length00)
llo = ta.lowest(low, length00)
midline = (hhi + llo) / 2
distance = (hhi - llo) * fmultiplier
upper_line_4_8 = midline + distance * 4
upper_line_3_8 = midline + distance * 3
upper_line_2_8 = midline + distance * 2
upper_line_1_8 = midline + distance * 1
lower_line_1_8 = midline - distance * 1
lower_line_2_8 = midline - distance * 2
lower_line_3_8 = midline - distance * 3
lower_line_4_8 = midline - distance * 4

// Plot Murrey Math lines
plot(upper_line_4_8, color=color.green, linewidth=1)
plot(upper_line_3_8, color=color.gray, linewidth=1)
plot(upper_line_2_8, color=color.gray, linewidth=1)
plot(upper_line_1_8, color=color.gray, linewidth=1)
plot(midline, color=color.yellow, linewidth=3)
plot(lower_line_1_8, color=color.gray, linewidth=1)
plot(lower_line_2_8, color=color.gray, linewidth=1)
plot(lower_line_3_8, color=color.gray, linewidth=1)
plot(lower_line_4_8, color=color.red, linewidth=1)

// EMA
len = input.int(16, minval=1, title="EMA Length")
src = input(close, title="EMA Source")
out = ta.ema(src, len)
plot(out, title="EMA", color=color.blue)

// Entry and exit signals

buy_signal = ta.crossover(close, lower_line_4_8) and out < midline
sell_signal = ta.crossunder(close, upper_line_4_8) and out > midline

ema_buy = ta.crossover(out,lower_line_4_8)
ema_sell = ta.crossunder(out,upper_line_4_8)

// Webhook Alert Messages (Fixed - No timestamp needed)
buy_alert_message = '{"coin": "' + coin_symbol + '", "action": "BUY", "market_order": "1"}'
sell_alert_message = '{"coin": "' + coin_symbol + '", "action": "SELL", "market_order": "1"}'

// Strategy logic with webhook alerts
if (buy_signal) or (ema_buy)
    if enable_webhooks
        strategy.entry("Buy", strategy.long, qty=1, comment="Buy", alert_message=buy_alert_message)
    else
        strategy.entry("Buy", strategy.long, qty=1, comment="Buy")

else if (sell_signal) or (ema_sell)
    if enable_webhooks
        strategy.entry("Sell", strategy.short, qty=1, comment="Sell", alert_message=sell_alert_message)
    else
        strategy.entry("Sell", strategy.short, qty=1, comment="Sell")

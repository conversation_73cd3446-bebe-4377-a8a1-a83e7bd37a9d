# 🚀 Railway Deployment Guide

## Step 1: Push Code to GitHub (Manual)

Since there might be authentication issues, please do this manually:

1. **Open GitHub Desktop** or use command line with your credentials
2. **Navigate to your project folder**: `C:\Projects\TRADINGVIEW`
3. **Push all files to**: https://github.com/mikeaper323/webhook

### Files to Include:
✅ **Essential Files:**
- `app.py` (main Flask application)
- `requirements.txt` (Python dependencies)
- `nixpacks.toml` (Railway configuration)
- `Procfile` (alternative Railway configuration)
- `README.md` (documentation)
- `.gitignore` (excludes sensitive files)

❌ **Files to EXCLUDE** (should be in .gitignore):
- `.env` (contains your API keys - NEVER commit this!)
- `venv/` (virtual environment)
- `*.log` files
- `__pycache__/`

## Step 2: Deploy on Railway

### 2.1 Sign Up and Connect GitHub

1. **Go to**: https://railway.app
2. **Sign up** with your GitHub account
3. **Click**: "New Project"
4. **Select**: "Deploy from GitHub repo"
5. **Choose**: `mikeaper323/webhook`

### 2.2 Configure Environment Variables

⚠️ **CRITICAL**: Add these environment variables in Railway dashboard:

```env
BINANCE_API_KEY=77cuHg5yYKofDOXNB6oxXoah264iZHIRxem1kOj2FlRdHhTe0qe62lV1z2aJCmBS
BINANCE_SECRET_KEY=s3OUFtZZBJLAbvzsrJ0XnESuIKoWj2r3hq8kMbOCXX7vR627mmKAkjTG6si4wmo2
FLASK_ENV=production
SECRET_KEY=d7f8e9a2b4c6d8e0f1a3b5c7d9e1f2a3b4c6d8e0f1a3b5c7d9e1f2a3b
WEBHOOK_SECRET=itsMike818!
PORT=8000
FIXED_QUANTITY_BTCUSDT=0.001
FIXED_QUANTITY_ETHUSDT=0.01
FIXED_QUANTITY_BTCUSD=0.001
FIXED_QUANTITY_ETHUSD=0.01
QUANTITY_PERCENTAGE=50
MAX_QUANTITY_BTCUSDT=0.1
MAX_QUANTITY_ETHUSDT=1.0
MAX_QUANTITY_BTCUSD=0.1
MAX_QUANTITY_ETHUSD=1.0
EMERGENCY_STOP=false
ALLOWED_SYMBOLS=BTCUSDT,BTCUSD
```

### 2.3 Add Environment Variables in Railway

1. **Go to your Railway project dashboard**
2. **Click**: "Variables" tab
3. **Add each variable** from the list above
4. **Click**: "Add" for each one

## Step 3: Get Your Public URL

1. **Go to**: "Settings" → "Networking"
2. **Click**: "Generate Domain"
3. **Copy your URL**: `https://your-app-name.up.railway.app`

## Step 4: Test Your Deployment

### 4.1 Health Check
```bash
curl https://your-app-name.up.railway.app/
```

### 4.2 Webhook Test
```bash
curl -X POST https://your-app-name.up.railway.app/webhook \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Secret: itsMike818!" \
  -d '{"coin": "BTC", "action": "BUY", "market_order": "1"}'
```

## Step 5: Update Pine Script

Replace the webhook URL in your Pine Script:

```pinescript
// Webhook Configuration
webhook_url = input.string("https://your-app-name.up.railway.app/webhook", title="Webhook URL", group="Webhook Settings")
webhook_secret = input.string("itsMike818!", title="Webhook Secret", group="Webhook Settings")
coin_symbol = input.string("BTC", title="Coin Symbol (BTC, ETH, etc.)", group="Webhook Settings")
enable_webhooks = input.bool(true, title="Enable Webhook Alerts", group="Webhook Settings")
```

## Step 6: Set Up TradingView Alert

1. **Add Pine Script** to your TradingView chart
2. **Create Alert**:
   - **Condition**: Your Murrey Math Strategy
   - **Webhook URL**: `https://your-app-name.up.railway.app/webhook`
   - **Message**: `{{strategy.order.alert_message}}`
   - **Headers**: `X-Webhook-Secret: itsMike818!`

## 🎉 Final Result

Your system will be:
- ✅ **Always online** (24/7)
- ✅ **Publicly accessible** to TradingView
- ✅ **Automatically trading** when signals trigger
- ✅ **Logging everything** for monitoring

## 🔧 Troubleshooting

### Deployment Failed?
- Check Railway logs for errors
- Verify all environment variables are set
- Ensure `requirements.txt` is correct

### Webhook Not Working?
- Test the public URL with curl
- Check Railway logs for incoming requests
- Verify webhook secret matches

### Trading Not Executing?
- Check Binance API keys are correct
- Verify account has sufficient balance
- Check allowed symbols configuration

## 📊 Monitoring

- **Railway Dashboard**: View logs and metrics
- **Webhook Logs**: Monitor incoming TradingView alerts
- **Binance Account**: Check trade executions

Your automated trading system will be live! 🚀

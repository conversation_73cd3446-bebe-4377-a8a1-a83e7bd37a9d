# Complete Step-by-Step Guide: Custom Python Flask Webhook for TradingView → Binance US

I'll walk you through building a **completely free** Python Flask webhook server that receives TradingView alerts and executes trades on Binance US automatically.

## **Phase 1: Environment Setup & Prerequisites**

### **1.1 Install Required Software**
```bash
# Install Python 3.9+ (if not already installed)
# Windows: Download from python.org
# Mac: brew install python
# Linux: apt install python3 python3-pip

# Verify installation
python --version
pip --version
```

### **1.2 Create Project Directory**
```bash
mkdir tradingview-binance-webhook
cd tradingview-binance-webhook

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Mac/Linux:
source venv/bin/activate
```

### **1.3 Install Python Dependencies**
```bash
# Create requirements.txt file
cat > requirements.txt << EOF
Flask==2.3.3
python-binance==1.0.19
python-dotenv==1.0.0
gunicorn==21.2.0
requests==2.31.0
cryptography==41.0.4
EOF

# Install all dependencies
pip install -r requirements.txt
```

## **Phase 2: Binance US API Setup**

### **2.1 Create Binance US API Keys**
1. **Log into Binance.US** account
2. **Navigate**: Profile Menu → **API Management**
3. **Click**: "Create API" 
4. **Name it**: "TradingView Webhook Bot"
5. **Complete 2FA verification**
6. **Set Permissions**:
   - ✅ **Enable Read**
   - ✅ **Enable Spot & Margin Trading**
   - ✅ **Enable Futures** (if you plan to use futures)
   - ❌ **Disable Withdrawals** (for security)
7. **Restrictions**: Select "Unrestricted" or add your server IP later
8. **Copy and save** API Key and Secret Key immediately

### **2.2 Test API Connection**
Create `test_api.py`:
```python
from binance.client import Client
import os
from dotenv import load_dotenv

load_dotenv()

# Test connection
api_key = 'YOUR_API_KEY_HERE'
api_secret = 'YOUR_SECRET_KEY_HERE'

client = Client(api_key, api_secret, tld='us')

try:
    # Test connection
    account = client.get_account()
    print("✅ Binance US API connection successful!")
    print(f"Account status: {account['accountType']}")
    
    # Get balances
    balances = client.get_account()['balances']
    for balance in balances:
        if float(balance['free']) > 0:
            print(f"{balance['asset']}: {balance['free']}")
            
except Exception as e:
    print(f"❌ API connection failed: {e}")
```

Run test:
```bash
python test_api.py
```

## **Phase 3: Flask Webhook Server Development**

### **3.1 Create Environment Variables**
Create `.env` file:
```bash
# .env file (keep this secret!)
BINANCE_API_KEY=your_actual_api_key_here
BINANCE_SECRET_KEY=your_actual_secret_key_here
FLASK_ENV=production
SECRET_KEY=your_random_secret_key_here
WEBHOOK_SECRET=your_webhook_secret_password
```

### **3.2 Main Flask Application**
Create `app.py`:
```python
import os
import json
import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_DOWN
from flask import Flask, request, jsonify
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default-secret-key')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Binance client
try:
    client = Client(
        os.getenv('BINANCE_API_KEY'),
        os.getenv('BINANCE_SECRET_KEY'),
        tld='us'  # Important: Use Binance US
    )
    logger.info("✅ Binance US client initialized successfully")
except Exception as e:
    logger.error(f"❌ Failed to initialize Binance client: {e}")
    client = None

class TradingBot:
    def __init__(self, binance_client):
        self.client = binance_client
        self.min_order_sizes = {
            'BTCUSD': 0.00001,
            'BTCUSDT': 0.00001,
            'ETHUSD': 0.001,
            'ETHUSDT': 0.001,
        }
        
    def get_account_balance(self, asset):
        """Get available balance for specific asset"""
        try:
            account = self.client.get_account()
            for balance in account['balances']:
                if balance['asset'] == asset:
                    return float(balance['free'])
            return 0.0
        except Exception as e:
            logger.error(f"Error getting balance for {asset}: {e}")
            return 0.0
    
    def calculate_quantity(self, symbol, side, percentage=90):
        """Calculate order quantity based on available balance"""
        try:
            if side.upper() == 'BUY':
                # For buy orders, use quote asset (USDT/USD)
                if 'USDT' in symbol:
                    quote_asset = 'USDT'
                else:
                    quote_asset = 'USD'
                
                balance = self.get_account_balance(quote_asset)
                # Use percentage of available balance
                usable_balance = balance * (percentage / 100)
                
                # Get current price
                ticker = self.client.get_symbol_ticker(symbol=symbol)
                current_price = float(ticker['price'])
                
                # Calculate quantity
                quantity = usable_balance / current_price
                
            else:  # SELL
                # For sell orders, use base asset (BTC/ETH)
                base_asset = symbol.replace('USD', '').replace('T', '')
                quantity = self.get_account_balance(base_asset) * (percentage / 100)
            
            # Round down to avoid insufficient balance errors
            min_qty = self.min_order_sizes.get(symbol, 0.00001)
            quantity = max(quantity, min_qty)
            
            # Round to appropriate decimal places
            if symbol.startswith('BTC'):
                quantity = float(Decimal(str(quantity)).quantize(Decimal('0.00001'), rounding=ROUND_DOWN))
            else:
                quantity = float(Decimal(str(quantity)).quantize(Decimal('0.001'), rounding=ROUND_DOWN))
                
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating quantity: {e}")
            return 0.0
    
    def place_market_order(self, symbol, side, custom_quantity=None):
        """Place market order on Binance US"""
        try:
            if not self.client:
                raise Exception("Binance client not initialized")
            
            # Calculate quantity if not provided
            if custom_quantity:
                quantity = float(custom_quantity)
            else:
                quantity = self.calculate_quantity(symbol, side)
            
            if quantity <= 0:
                raise Exception(f"Invalid quantity: {quantity}")
            
            logger.info(f"Placing {side} order: {quantity} {symbol}")
            
            # Place market order
            if side.upper() == 'BUY':
                order = self.client.order_market_buy(
                    symbol=symbol,
                    quantity=quantity
                )
            else:
                order = self.client.order_market_sell(
                    symbol=symbol,
                    quantity=quantity
                )
            
            logger.info(f"✅ Order successful: {order['orderId']}")
            return {
                'success': True,
                'order_id': order['orderId'],
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'status': order['status']
            }
            
        except BinanceAPIException as e:
            logger.error(f"Binance API error: {e}")
            return {'success': False, 'error': f'Binance API error: {e}'}
        except BinanceOrderException as e:
            logger.error(f"Binance order error: {e}")
            return {'success': False, 'error': f'Order error: {e}'}
        except Exception as e:
            logger.error(f"General error: {e}")
            return {'success': False, 'error': str(e)}

# Initialize trading bot
trading_bot = TradingBot(client) if client else None

@app.route('/', methods=['GET'])
def index():
    """Health check endpoint"""
    return jsonify({
        'status': 'active',
        'message': 'TradingView → Binance US Webhook Bot',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'binance_status': 'connected' if client else 'disconnected'
    })

@app.route('/webhook', methods=['POST'])
def webhook():
    """Main webhook endpoint for TradingView alerts"""
    try:
        # Get webhook data
        data = request.get_json()
        
        if not data:
            logger.warning("No JSON data received")
            return jsonify({'error': 'No JSON data received'}), 400
        
        logger.info(f"Received webhook data: {json.dumps(data, indent=2)}")
        
        # Validate webhook secret (optional security)
        webhook_secret = request.headers.get('X-Webhook-Secret')
        expected_secret = os.getenv('WEBHOOK_SECRET')
        
        if expected_secret and webhook_secret != expected_secret:
            logger.warning("Invalid webhook secret")
            return jsonify({'error': 'Unauthorized'}), 401
        
        # Extract required fields
        action = data.get('action', '').upper()
        symbol = data.get('symbol', '').upper()
        
        if not action or not symbol:
            return jsonify({'error': 'Missing action or symbol'}), 400
        
        if action not in ['BUY', 'SELL']:
            return jsonify({'error': 'Invalid action. Use BUY or SELL'}), 400
        
        # Optional: Get custom quantity
        quantity = data.get('quantity')
        
        # Execute trade
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        result = trading_bot.place_market_order(symbol, action, quantity)
        
        if result['success']:
            response = {
                'status': 'success',
                'message': f'{action} order executed successfully',
                'order_details': result,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            return jsonify(response), 200
        else:
            response = {
                'status': 'error',
                'message': 'Order execution failed',
                'error': result['error'],
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            return jsonify(response), 400
        
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        return jsonify({
            'status': 'error',
            'message': 'Internal server error',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 500

@app.route('/test', methods=['POST'])
def test_order():
    """Test endpoint for manual testing"""
    try:
        data = request.get_json() or {}
        action = data.get('action', 'BUY').upper()
        symbol = data.get('symbol', 'BTCUSDT').upper()
        
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        # Place test order
        result = trading_bot.place_market_order(symbol, action)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Test order error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/balance', methods=['GET'])
def get_balance():
    """Get account balance"""
    try:
        if not client:
            return jsonify({'error': 'Binance client not initialized'}), 500
        
        account = client.get_account()
        balances = []
        
        for balance in account['balances']:
            if float(balance['free']) > 0 or float(balance['locked']) > 0:
                balances.append({
                    'asset': balance['asset'],
                    'free': balance['free'],
                    'locked': balance['locked']
                })
        
        return jsonify({
            'balances': balances,
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
        
    except Exception as e:
        logger.error(f"Balance error: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
```

### **3.3 Test Locally**
```bash
# Test the Flask app locally
python app.py

# In another terminal, test webhook
curl -X POST http://localhost:5000/test \
  -H "Content-Type: application/json" \
  -d '{"action": "BUY", "symbol": "BTCUSDT"}'

# Test balance endpoint
curl http://localhost:5000/balance
```

## **Phase 4: Deploy to Railway (Free)**

### **4.1 Prepare for Deployment**

Create `nixpacks.toml`:
```toml
# nixpacks.toml
[start]
cmd = "gunicorn app:app"

[variables]
PORT = "8000"
```

Create `Procfile` (alternative):
```
web: gunicorn app:app
```

### **4.2 Deploy to Railway**

**Option A: GitHub Deployment (Recommended)**
```bash
# Initialize git repository
git init
git add .
git commit -m "Initial TradingView Binance webhook bot"

# Push to GitHub
# 1. Create new repository on GitHub
# 2. Add remote origin
git remote add origin https://github.com/yourusername/tradingview-binance-webhook.git
git push -u origin main
```

**Deploy on Railway:**
1. Go to [railway.app](https://railway.app) and sign up/login
2. Click **"New Project"**
3. Select **"Deploy from GitHub repo"**
4. Choose your webhook repository
5. Railway auto-detects Python and deploys

**Option B: Railway CLI Deployment**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Initialize project
railway init

# Deploy
railway up
```

### **4.3 Configure Environment Variables in Railway**
1. Go to **Railway Dashboard → Your Project**
2. Click **"Variables"** tab
3. Add environment variables:
   ```
   BINANCE_API_KEY=your_api_key_here
   BINANCE_SECRET_KEY=your_secret_key_here
   SECRET_KEY=your_random_secret_key
   WEBHOOK_SECRET=your_webhook_password
   FLASK_ENV=production
   ```

### **4.4 Get Your Public URL**
1. Go to **Settings → Networking**
2. Click **"Generate Domain"**
3. Copy your webhook URL: `https://your-app-name.up.railway.app`

## **Phase 5: Modify Your Pine Script Strategy**

### **5.1 Updated Pine Script with Webhook Alerts**
```pinescript
//@version=5
strategy(title="[RS]Murrey's Math Lines Channel Strategy", overlay=true)

// Your existing parameters (keep as-is)
length00 = input.int(115, title="Length", minval=1)
fmultiplier = input.float(0.121, title="Multiplier", step=0.001)

// Webhook configuration
webhook_url = input.string("https://your-app-name.up.railway.app/webhook", title="Webhook URL")
symbol_name = input.string("BTCUSDT", title="Trading Symbol")
trade_quantity = input.string("", title="Quantity (leave empty for auto)")

// Your existing Murrey Math calculations (keep all as-is)
hhi = ta.highest(high, length00)
llo = ta.lowest(low, length00)
midline = (hhi + llo) / 2
distance = (hhi - llo) * fmultiplier
upper_line_4_8 = midline + distance * 4
upper_line_3_8 = midline + distance * 3
upper_line_2_8 = midline + distance * 2
upper_line_1_8 = midline + distance * 1
lower_line_1_8 = midline - distance * 1
lower_line_2_8 = midline - distance * 2
lower_line_3_8 = midline - distance * 3
lower_line_4_8 = midline - distance * 4

// Plot lines (keep as-is)
plot(upper_line_4_8, color=color.green, linewidth=1)
plot(upper_line_3_8, color=color.gray, linewidth=1)
plot(upper_line_2_8, color=color.gray, linewidth=1)
plot(upper_line_1_8, color=color.gray, linewidth=1)
plot(midline, color=color.yellow, linewidth=3)
plot(lower_line_1_8, color=color.gray, linewidth=1)
plot(lower_line_2_8, color=color.gray, linewidth=1)
plot(lower_line_3_8, color=color.gray, linewidth=1)
plot(lower_line_4_8, color=color.red, linewidth=1)

// EMA (keep as-is)
len = input.int(16, minval=1, title="EMA Length")
src = input(close, title="EMA Source")
out = ta.ema(src, len)
plot(out, title="EMA", color=color.blue)

// Your existing signal logic (keep as-is)
buy_signal = ta.crossover(close, lower_line_4_8) and out < midline
sell_signal = ta.crossunder(close, upper_line_4_8) and out > midline
ema_buy = ta.crossover(out, lower_line_4_8)
ema_sell = ta.crossunder(out, upper_line_4_8)

// Webhook alert messages
buy_message = '{"action": "BUY", "symbol": "' + symbol_name + '", "quantity": "' + trade_quantity + '", "price": ' + str(close) + ', "timestamp": "' + str(time) + '"}'
sell_message = '{"action": "SELL", "symbol": "' + symbol_name + '", "quantity": "' + trade_quantity + '", "price": ' + str(close) + ', "timestamp": "' + str(time) + '"}'

// Strategy execution with webhook alerts
if (buy_signal) or (ema_buy)
    strategy.entry("Buy", strategy.long, qty=1, comment="Buy", alert_message=buy_message)
    
if (sell_signal) or (ema_sell)
    strategy.entry("Sell", strategy.short, qty=1, comment="Sell", alert_message=sell_message)
```

## **Phase 6: Configure TradingView Alerts**

### **6.1 Create Strategy Alert in TradingView**
1. **Add your modified strategy** to a chart
2. **Open Strategy Tester** panel
3. **Click "Add Alert"** from strategy panel
4. **Configure Alert**:
   - **Condition**: Your strategy name
   - **Options**: "Once Per Bar Close" 
   - **Actions**: ✅ **Webhook URL**
   - **Webhook URL**: `https://your-app-name.up.railway.app/webhook`
   - **Message**: `{{strategy.order.alert_message}}`
   - **Name**: "Murrey Math Auto Trading"

### **6.2 Advanced Alert Configuration (Optional)**
Add webhook secret header for security:
- **Additional Headers**: `X-Webhook-Secret: your_webhook_password`

## **Phase 7: Testing & Monitoring**

### **7.1 Test Complete Pipeline**
```bash
# Test webhook directly
curl -X POST https://your-app-name.up.railway.app/webhook \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Secret: your_webhook_password" \
  -d '{
    "action": "BUY",
    "symbol": "BTCUSDT", 
    "quantity": "0.001",
    "price": 45000,
    "timestamp": "1699564800000"
  }'

# Check bot status
curl https://your-app-name.up.railway.app/

# Check balance
curl https://your-app-name.up.railway.app/balance
```

### **7.2 Monitor Logs**
In Railway dashboard:
1. Go to **"Deployments"** tab
2. Click **"View Logs"**
3. Monitor real-time execution logs

### **7.3 Common Issues & Solutions**

**Issue: "Insufficient balance"**
```python
# Solution: Adjust percentage in calculate_quantity()
quantity = self.calculate_quantity(symbol, side, percentage=50)  # Use 50% instead of 90%
```

**Issue: "Invalid symbol"**
- Ensure symbol matches Binance US format: `BTCUSDT`, `ETHUSD`
- Check available trading pairs in Binance US

**Issue: "Order size below minimum"**
```python
# Update min_order_sizes in TradingBot class
self.min_order_sizes = {
    'BTCUSDT': 0.00001,  # Adjust based on current Binance US requirements
    'ETHUSD': 0.001,
}
```

## **Phase 8: Security & Production Considerations**

### **8.1 Security Best Practices**
1. **Use environment variables** for all secrets
2. **Enable webhook secret** authentication
3. **Set API key restrictions** to your server IP
4. **Never enable withdrawal permissions**
5. **Monitor API usage** regularly

### **8.2 Production Optimizations**
```python
# Add rate limiting
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["100 per hour"]
)

@app.route('/webhook', methods=['POST'])
@limiter.limit("10 per minute")  # Max 10 trades per minute
def webhook():
    # Existing code...
```

### **8.3 Backup & Recovery**
```python
# Add order history logging
def log_trade(order_result):
    with open('trade_history.json', 'a') as f:
        trade_record = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'order': order_result
        }
        f.write(json.dumps(trade_record) + '\n')
```

## **Phase 9: Cost Analysis**

| Service | Cost | Notes |
|---------|------|-------|
| **Railway Hosting** | $0 (Free tier) | 500 hours/month, $5 credit |
| **TradingView Pro** | $14.95/month | Required for webhook alerts |
| **Binance US Trading** | 0.1% per trade | Standard trading fees |
| **Domain (Optional)** | $10-15/year | Custom domain for webhook |

**Total Monthly Cost: ~$15** (mostly TradingView subscription)

## **Phase 10: Going Live**

### **10.1 Final Checklist**
- ✅ Flask app deployed and accessible
- ✅ Environment variables configured
- ✅ Binance US API keys tested
- ✅ Pine Script modified with webhook alerts
- ✅ TradingView alert created and active
- ✅ Test trades executed successfully
- ✅ Logs monitoring enabled

### **10.2 Start Trading**
1. **Enable TradingView alert**
2. **Monitor Railway logs** for incoming webhooks
3. **Check Binance US** for executed trades
4. **Adjust parameters** based on performance

### **10.3 Scaling Up**
```python
# Add multiple symbol support
TRADING_PAIRS = ['BTCUSDT', 'ETHUSD', 'ADAUSD', 'DOTUSD']

# Add position sizing based on portfolio percentage
def calculate_portfolio_position(symbol, portfolio_percentage=10):
    total_usd_value = get_total_portfolio_value()
    position_size = total_usd_value * (portfolio_percentage / 100)
    return position_size
```

Your **completely free** TradingView → Binance US webhook automation system is now live! The setup provides professional-grade automated trading with minimal ongoing costs, giving you the power to execute your Murrey Math strategy 24/7 without manual intervention.

[1](https://www.youtube.com/watch?v=gMRee2srpe8)
[2](https://www.youtube.com/watch?v=1nGSDl_-YeI)
[3](https://www.reddit.com/r/TradingView/comments/120q6bt/auto_trading_with_binance_from_strategy_signal/)
[4](https://www.youtube.com/watch?v=-wT9h9Nc9sk)
[5](https://wundertrading.com/journal/en/learn/article/tradingview-alerts-binance-us)
[6](https://seenode.com/blog/flask-app-heroku-deployment-beginners-guide/)
[7](https://www.youtube.com/watch?v=ZiBBVYB5PuU)
[8](https://techiejackieblogs.com/how-to-create-a-trading-bot-using-tradingview-python-and-heroku/)
[9](https://www.codecademy.com/article/deploying-a-flask-app)
[10](https://support.binance.us/en/articles/9843443-binance-us-launches-new-api-documentation-portal-for-traders-and-developers)
[11](https://github.com/51bitquant/binance-tradingview-webhook-bot)
[12](https://realpython.com/flask-by-example-part-1-project-setup/)
[13](https://algotrading101.com/learn/binance-python-api-guide/)
[14](https://stackoverflow.com/questions/60575756/how-to-use-http-requests-from-webhooks-to-make-trade-using-binance-api)
[15](https://www.reddit.com/r/flask/comments/1jnqvun/what_is_the_best_website_to_deploy_a_flask_app_in/)
[16](https://docs.binance.us)
[17](https://www.tradingview.com/support/solutions/43000529348-how-to-configure-webhook-alerts/)
[18](https://www.heroku.com/pricing/)
[19](https://python-binance.readthedocs.io)
[20](https://stackoverflow.com/questions/66868694/how-do-i-deploy-a-flask-app-using-a-mongodb-database-to-heroku)
[21](https://www.youtube.com/watch?v=Ce_V-DSJae8)
[22](https://railway.com/deploy/igzwwg)
[23](https://docs.railway.com/guides/flask)
[24](https://www.youtube.com/watch?v=T8rLXm2eVlk)
[25](https://www.youtube.com/watch?v=KdytNxveQo0)
[26](https://stackoverflow.com/questions/********/problem-with-python-binance-api-order-market-buy-and-order-market-sell)
[27](https://dev.to/ankur0904/hosting-a-flask-web-server-on-railway-free-1049)
[28](https://render.com/docs/deploy-flask)
[29](https://python-binance.readthedocs.io/en/latest/account.html)
[30](https://gist.github.com/tech-savvy-guy/0dc7d3383ac3659e6db9e0a3bf536386)
[31](https://www.reddit.com/r/learnpython/comments/13x8rbg/hosting_a_flask_app_for_free/)
[32](https://jorgestutorials.com/binancebot.html)
[33](https://hostadvice.com/python-hosting/flask-hosting/free/)
[34](https://testdriven.io/blog/flask-render-deployment/)
[35](https://github.com/sammchardy/python-binance)
[36](https://www.linkedin.com/posts/ankur-singh-161458227_hosting-a-flask-web-server-on-railway-free-activity-7191054000413167618-y1Oh)
[37](https://stackoverflow.com/questions/********/hosting-a-flask-web-app-with-a-domain-i-already-own)
[38](https://python-binance.readthedocs.io/en/latest/binance.html)
[39](https://www.youtube.com/watch?v=Dli5Hhgxq2Y)
#!/usr/bin/env python3
"""
Test script to verify if your webhook is publicly accessible
"""

import requests
import json
import time
import sys

def test_public_url(public_url):
    """Test if a public URL can reach your webhook"""
    print(f"🧪 Testing Public URL: {public_url}")
    print("=" * 60)
    
    # Test 1: Health Check
    print("1️⃣ Testing Health Check...")
    try:
        response = requests.get(f"{public_url}/", timeout=10)
        if response.status_code == 200:
            print("✅ Health check successful!")
            data = response.json()
            print(f"   Status: {data.get('status')}")
            print(f"   Binance: {data.get('binance_status')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: Webhook Endpoint
    print("\n2️⃣ Testing Webhook Endpoint...")
    webhook_payload = {
        "coin": "BTC",
        "action": "BUY", 
        "market_order": "1"
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Webhook-Secret": "itsMike818!"
    }
    
    try:
        response = requests.post(f"{public_url}/webhook", 
                               json=webhook_payload, 
                               headers=headers, 
                               timeout=15)
        
        if response.status_code == 200:
            print("✅ Webhook test successful!")
            data = response.json()
            print(f"   Status: {data.get('status')}")
            print(f"   Message: {data.get('message')}")
            if 'order_details' in data:
                order = data['order_details']
                print(f"   Order ID: {order.get('order_id')}")
                print(f"   Symbol: {order.get('symbol')}")
                print(f"   Quantity: {order.get('quantity')}")
        else:
            print(f"❌ Webhook test failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Webhook test error: {e}")
        return False
    
    # Test 3: Response Time
    print("\n3️⃣ Testing Response Time...")
    try:
        start_time = time.time()
        response = requests.get(f"{public_url}/", timeout=10)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        print(f"✅ Response time: {response_time:.2f}ms")
        
        if response_time < 1000:
            print("   🚀 Excellent response time!")
        elif response_time < 3000:
            print("   ⚡ Good response time")
        else:
            print("   ⚠️  Slow response time - may cause TradingView timeouts")
            
    except Exception as e:
        print(f"❌ Response time test error: {e}")
    
    print("\n🎉 All tests passed! TradingView should be able to reach your webhook.")
    return True

def get_ngrok_url():
    """Try to get ngrok URL from local API"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
        if response.status_code == 200:
            tunnels = response.json()['tunnels']
            if tunnels:
                for tunnel in tunnels:
                    if tunnel['proto'] == 'https':
                        return tunnel['public_url']
        return None
    except:
        return None

def main():
    """Main testing function"""
    print("🌐 Public Connectivity Test for TradingView Webhooks")
    print("=" * 60)
    
    # Check if local Flask app is running
    try:
        response = requests.get('http://localhost:8000/', timeout=5)
        if response.status_code == 200:
            print("✅ Local Flask app is running")
        else:
            print("❌ Local Flask app is not responding correctly")
            return
    except:
        print("❌ Local Flask app is not running on localhost:8000")
        print("   Please start it first: python app.py")
        return
    
    # Try to detect ngrok automatically
    ngrok_url = get_ngrok_url()
    if ngrok_url:
        print(f"🔍 Detected ngrok URL: {ngrok_url}")
        if test_public_url(ngrok_url):
            print(f"\n🎯 Use this URL in TradingView: {ngrok_url}/webhook")
            return
    
    # Manual URL input
    print("\n📝 Manual URL Testing")
    print("If you have a public URL (ngrok, Railway, etc.), enter it below:")
    
    public_url = input("Enter your public URL (without /webhook): ").strip()
    
    if not public_url:
        print("\n📋 Setup Instructions:")
        print("1. For ngrok:")
        print("   - Sign up: https://dashboard.ngrok.com/signup")
        print("   - Get authtoken and run: ngrok config add-authtoken YOUR_TOKEN")
        print("   - Start tunnel: ngrok http 8000")
        print("   - Copy the HTTPS URL")
        print("\n2. For Railway:")
        print("   - Push code to GitHub")
        print("   - Deploy on railway.app")
        print("   - Copy the deployment URL")
        return
    
    # Remove trailing slash and /webhook if present
    public_url = public_url.rstrip('/')
    if public_url.endswith('/webhook'):
        public_url = public_url[:-8]
    
    # Test the URL
    if test_public_url(public_url):
        print(f"\n🎯 Perfect! Use this URL in TradingView:")
        print(f"   Webhook URL: {public_url}/webhook")
        print(f"   Secret: itsMike818!")
        
        print(f"\n📋 TradingView Alert Settings:")
        print(f"   Condition: Your Murrey Math Strategy")
        print(f"   Webhook URL: {public_url}/webhook")
        print(f"   Message: {{{{strategy.order.alert_message}}}}")
        print(f"   Headers: X-Webhook-Secret: itsMike818!")
    else:
        print(f"\n❌ URL is not accessible. Please check:")
        print("   - URL is correct and complete")
        print("   - Service is running and accessible")
        print("   - No firewall blocking the connection")

if __name__ == "__main__":
    main()

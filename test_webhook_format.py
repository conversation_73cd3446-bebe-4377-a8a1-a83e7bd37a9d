#!/usr/bin/env python3
"""
Test script to verify TradingView webhook format compatibility
with the Flask webhook server.
"""

import requests
import json
import time

# Configuration
WEBHOOK_URL = "http://localhost:8000/webhook"
WEBHOOK_SECRET = "itsMike818!"  # Updated to match .env file
TEST_ENDPOINT = "http://localhost:8000/test"

def test_new_format():
    """Test the new Pine Script webhook format"""
    print("🧪 Testing New Pine Script Format...")
    
    # This matches the format your updated Pine Script will send
    payload = {
        "coin": "BTC",
        "action": "BUY",
        "market_order": "1",
        "timestamp": str(int(time.time() * 1000))
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Webhook-Secret": WEBHOOK_SECRET
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=payload, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure <PERSON>lask app is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_old_format():
    """Test the traditional webhook format"""
    print("\n🧪 Testing Traditional Format...")
    
    payload = {
        "action": "SELL",
        "symbol": "BTCUSD",
        "message": "Test alert",
        "timestamp": str(int(time.time() * 1000))
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Webhook-Secret": WEBHOOK_SECRET
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=payload, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Flask app is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_health_check():
    """Test the health check endpoint"""
    print("\n🏥 Testing Health Check...")
    
    try:
        response = requests.get("http://localhost:8000/")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Flask app is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_balance_check():
    """Test the balance endpoint"""
    print("\n💰 Testing Balance Check...")
    
    try:
        response = requests.get("http://localhost:8000/balance")
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"Error Response: {response.text}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Flask app is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 TradingView Webhook Format Testing")
    print("=" * 50)
    
    # Test health check first
    health_ok = test_health_check()
    
    if not health_ok:
        print("\n❌ Health check failed. Please start the Flask app first:")
        print("   python app.py")
        return
    
    # Test balance (this will show if Binance API is connected)
    balance_ok = test_balance_check()
    
    # Test webhook formats
    new_format_ok = test_new_format()
    old_format_ok = test_old_format()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Health Check: {'✅' if health_ok else '❌'}")
    print(f"   Balance Check: {'✅' if balance_ok else '❌'}")
    print(f"   New Format (Pine Script): {'✅' if new_format_ok else '❌'}")
    print(f"   Old Format (Traditional): {'✅' if old_format_ok else '❌'}")
    
    if new_format_ok:
        print("\n🎉 Your Pine Script webhook format is compatible!")
        print("   You can now set up TradingView alerts with your updated Pine Script.")
    else:
        print("\n⚠️  There may be issues with the webhook format.")
        print("   Check the Flask app logs for more details.")

if __name__ == "__main__":
    main()

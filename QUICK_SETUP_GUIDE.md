# 🚀 Quick Setup Guide: TradingView → Your Computer

## ✅ Current Status
- ✅ Flask webhook server: **Working perfectly**
- ✅ Binance US integration: **Connected and trading**
- ✅ Local testing: **All tests passed**
- ❌ Public access: **Needs setup (TradingView can't reach localhost)**

## 🎯 Next Step: Make Your Webhook Publicly Accessible

### Option A: ngrok (5 minutes - Recommended for testing)

1. **Sign up for free ngrok account**: https://dashboard.ngrok.com/signup
2. **Get your authtoken**: https://dashboard.ngrok.com/get-started/your-authtoken
3. **Configure ngrok**:
   ```bash
   ngrok config add-authtoken YOUR_TOKEN_HERE
   ```
4. **Start the tunnel**:
   ```bash
   ngrok http 8000
   ```
5. **Copy the HTTPS URL** (e.g., `https://abc123.ngrok.io`)

### Option B: Railway (30 minutes - Recommended for 24/7)

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "TradingView webhook bot"
   git push origin main
   ```
2. **Deploy on Railway**: https://railway.app
3. **Connect your GitHub repo**
4. **Add environment variables** (copy from your `.env` file)
5. **Get your permanent URL**

## 🧪 Test Your Public URL

Once you have a public URL, test it:

```bash
python test_public_connectivity.py
```

This will verify TradingView can reach your webhook.

## 📝 Update Your Pine Script

Replace the webhook URL in your Pine Script:

```pinescript
// For ngrok (temporary URL)
webhook_url = input.string("https://your-ngrok-url.ngrok.io/webhook", title="Webhook URL", group="Webhook Settings")

// For Railway (permanent URL)  
webhook_url = input.string("https://your-app.up.railway.app/webhook", title="Webhook URL", group="Webhook Settings")

// Keep these the same
webhook_secret = input.string("itsMike818!", title="Webhook Secret", group="Webhook Settings")
coin_symbol = input.string("BTC", title="Coin Symbol (BTC, ETH, etc.)", group="Webhook Settings")
enable_webhooks = input.bool(true, title="Enable Webhook Alerts", group="Webhook Settings")
```

## 🎯 TradingView Alert Setup

1. **Add Pine Script** to your chart
2. **Create Alert**:
   - **Condition**: Your Murrey Math Strategy
   - **Webhook URL**: Your public URL + `/webhook`
   - **Message**: `{{strategy.order.alert_message}}`
   - **Headers**: `X-Webhook-Secret: itsMike818!`

## 🎉 Final Result

Once set up, your system will:
1. **TradingView** detects Murrey Math signal
2. **Sends webhook** to your public URL
3. **Your Flask app** receives the alert
4. **Executes trade** on Binance US automatically
5. **Logs everything** for monitoring

## ⚡ Quick Start (Choose One)

### For Immediate Testing:
```bash
# 1. Sign up for ngrok (2 minutes)
# 2. Get authtoken and configure
ngrok config add-authtoken YOUR_TOKEN
ngrok http 8000
# 3. Copy HTTPS URL and use in TradingView
```

### For Production Use:
```bash
# 1. Push to GitHub
git add . && git commit -m "webhook bot" && git push
# 2. Deploy on Railway (railway.app)
# 3. Add environment variables
# 4. Use permanent URL in TradingView
```

Your webhook system is **ready and working** - you just need to make it publicly accessible! 🚀

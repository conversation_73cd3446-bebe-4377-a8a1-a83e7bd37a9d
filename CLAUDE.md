# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a Flask-based webhook automation system that connects TradingView alerts to Binance US trading orders. The system:

- **Receives webhooks** from TradingView alerts via POST requests
- **Validates webhook secrets** to ensure security
- **Calculates order quantities** based on percentage of available balance (from .env)
- **Executes market orders** on Binance US
- **Logs all activities** for troubleshooting and monitoring

Key architectural components:
- **Flask app** (`app.py`) - Main application with all endpoints and trading logic
- **TradingBot class** - Handles Binance API interactions and order calculations
- **Percentage-based quantity calculation** - Uses QUANTITY_PERCENTAGE from .env for sell orders
- **Environment-driven configuration** - All settings loaded from .env file
- **Automatic log clearing** - Clears logs on each application start

## Common Development Commands

### Environment Setup
```bash
# Activate virtual environment (VS Code does this automatically)
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Running the Application
```bash
# Start Flask development server
python app.py

# Test with production server (for deployment)
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

### Testing Endpoints
```bash
# Test webhook (uses percentage-based quantities)
curl -X POST http://localhost:8000/webhook \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Secret: testsecret" \
  -d '{"action": "SELL", "symbol": "BTCUSD"}'

# Test endpoint (same as webhook)
curl -X POST http://localhost:8000/test \
  -H "Content-Type: application/json" \
  -d '{"action": "BUY", "symbol": "BTCUSDT"}'

# Check system health
curl http://localhost:8000/

# Get account balance
curl http://localhost:8000/balance
```

### VS Code Tasks
- `Activate Environment` - Sets up virtual environment in terminal
- `Install Dependencies` - Installs required packages
- `Run Flask App` - Starts the application
- `Test API Connection` - Tests Binance API connectivity

## Configuration System

The system uses percentage-based quantity calculation determined by `.env` variables:

**Key Configuration:**
- `QUANTITY_PERCENTAGE=50` - Sells 50% of available BTC balance for sell orders
- `FIXED_QUANTITY_BTCUSDT=0.001` - Fixed fallback quantity
- `EMERGENCY_STOP=false` - Emergency stop feature
- `ALLOWED_SYMBOLS=BTCUSDT,BTCUSD` - Whitelist of symbols

**Quantity Calculation Logic:**
- For SELL orders: Calculates 50% of available BTC balance
- For BUY orders: Uses percentage of available USDT balance
- Applies Binance precision rules and step size constraints
- Falls back to fixed quantities if percentage calculation fails

## Webhook Processing Flow

1. **Receive webhook** with JSON payload containing action and symbol
2. **Validate webhook secret** from `X-Webhook-Secret` header
3. **Check emergency stop** and symbol whitelist
4. **Calculate quantity** using percentage-based method from .env
5. **Apply precision rules** based on Binance symbol info
6. **Execute market order** on Binance US
7. **Return response** with order details or error

## Security Features

- **Webhook secret validation** - All requests require proper secret header
- **Environment variable management** - API keys and secrets never in code
- **Emergency stop functionality** - Can disable trading via .env
- **Symbol whitelist** - Restricts trading to specified symbols only
- **Secure logging** - No sensitive data in logs

## Important Implementation Details

- **Always uses percentage-based quantities** for both buy and sell orders (QUANTITY_PERCENTAGE from .env)
- **Market orders only** - No limit orders to prevent price manipulation
- **Automatic precision handling** - Respects Binance's step size and quantity rules
- **Log clearing on startup** - Fresh logs for each application run
- **Binance US specific** - Uses tld='us' for Binance US API endpoints
- **Error handling with fallbacks** - Multiple layers of validation and error recovery

## File Structure

```
├── app.py              # Main Flask application and TradingBot class
├── .env                # Environment variables (API keys, configuration)
├── .env.template       # Template for environment variables
├── requirements.txt    # Python dependencies
├── README.md           # Comprehensive documentation
├── .gitignore          # Git ignore file
├── CLAUDE.md           # This file
├── .vscode/            # VS Code configuration
│   ├── settings.json   # Auto-activates virtual environment
│   └── tasks.json      # Convenience tasks for development
└── venv/               # Virtual environment
```

## Development Notes

- The system is designed for live trading with safety features
- All order quantities are calculated as percentages of available balance
- Test thoroughly before deploying to production
- Monitor `trading_bot.log` for detailed execution information
- The `/webhook` and `/test` endpoints both use the same trading logic
# 🌐 ngrok Setup Guide for TradingView Webhooks

Since TradingView servers can't reach your local computer directly, we need to create a **public tunnel** using ngrok.

## 🚀 Quick Setup (5 minutes)

### Step 1: Download ngrok

1. **Go to**: https://ngrok.com/download
2. **Download** the Windows version
3. **Extract** the `ngrok.exe` file to your project folder

### Step 2: Create Free Account (Optional but Recommended)

1. **Sign up** at https://dashboard.ngrok.com/signup
2. **Get your authtoken** from https://dashboard.ngrok.com/get-started/your-authtoken
3. **Run**: `ngrok config add-authtoken YOUR_TOKEN_HERE`

### Step 3: Start Your Flask App

```bash
# Make sure your Flask app is running
python app.py
```

### Step 4: Start ngrok Tunnel

Open a **new terminal** and run:
```bash
ngrok http 8000
```

You'll see output like:
```
Session Status                online
Account                       <EMAIL>
Version                       3.x.x
Region                        United States (us)
Latency                       -
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:8000
```

### Step 5: Copy Your Public URL

From the ngrok output, copy the **HTTPS URL** (e.g., `https://abc123.ngrok.io`)

## 🧪 Test the Public URL

### Test 1: Health Check
```bash
curl https://your-ngrok-url.ngrok.io/
```

### Test 2: Webhook Test
```bash
curl -X POST https://your-ngrok-url.ngrok.io/webhook \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Secret: itsMike818!" \
  -d '{"coin": "BTC", "action": "BUY", "market_order": "1"}'
```

## 📝 Update Your Pine Script

In your Pine Script, change the webhook URL to your ngrok URL:

```pinescript
// Webhook Configuration
webhook_url = input.string("https://your-ngrok-url.ngrok.io/webhook", title="Webhook URL", group="Webhook Settings")
webhook_secret = input.string("itsMike818!", title="Webhook Secret", group="Webhook Settings")
coin_symbol = input.string("BTC", title="Coin Symbol (BTC, ETH, etc.)", group="Webhook Settings")
enable_webhooks = input.bool(true, title="Enable Webhook Alerts", group="Webhook Settings")
```

## 🎯 TradingView Alert Setup

1. **Add your Pine Script** to TradingView chart
2. **Configure strategy settings** with your ngrok URL
3. **Create Alert**:
   - **Condition**: Your Murrey Math strategy
   - **Webhook URL**: `https://your-ngrok-url.ngrok.io/webhook`
   - **Message**: `{{strategy.order.alert_message}}`
   - **Headers**: `X-Webhook-Secret: itsMike818!`

## ⚠️ Important Notes

### Security
- **ngrok URLs are public** - anyone with the URL can access your webhook
- **Use webhook secret** for authentication (`itsMike818!`)
- **Monitor your logs** for any suspicious activity

### Limitations
- **Free ngrok** URLs change each time you restart
- **8-hour session limit** on free accounts
- **40 connections/minute** limit on free accounts

### For Production
Consider deploying to:
- **Railway** (free tier available)
- **Heroku** (free tier discontinued but paid plans available)
- **Render** (free tier available)
- **DigitalOcean App Platform**

## 🔧 Troubleshooting

### ngrok Not Working?
1. **Check firewall** - allow ngrok.exe through Windows Firewall
2. **Try different region**: `ngrok http 8000 --region eu`
3. **Check antivirus** - some antivirus software blocks ngrok

### TradingView Can't Reach Webhook?
1. **Verify ngrok URL** is accessible from browser
2. **Check webhook secret** matches exactly
3. **Monitor Flask logs** for incoming requests
4. **Test with curl** first before TradingView

### Connection Issues?
1. **Restart ngrok** if URL becomes unresponsive
2. **Check Flask app** is still running
3. **Verify port 8000** is not blocked

## 🎉 Success Indicators

You'll know it's working when:
- ✅ ngrok shows "online" status
- ✅ Public URL responds to health check
- ✅ Webhook test returns success
- ✅ Flask logs show incoming requests
- ✅ TradingView alerts trigger trades

Your system will then be **fully automated** - TradingView → ngrok → Your Flask App → Binance US! 🚀
